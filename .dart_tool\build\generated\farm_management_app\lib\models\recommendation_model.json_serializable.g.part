// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecommendationModel _$RecommendationModelFromJson(Map<String, dynamic> json) =>
    RecommendationModel(
      id: json['id'] as String,
      farmId: json['farmId'] as String,
      cropId: json['cropId'] as String,
      type: $enumDecode(_$RecommendationTypeEnumMap, json['type']),
      title: json['title'] as String,
      description: json['description'] as String,
      priority: $enumDecode(_$RecommendationPriorityEnumMap, json['priority']),
      actions: (json['actions'] as List<dynamic>)
          .map((e) => RecommendationAction.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      implementedAt: json['implementedAt'] == null
          ? null
          : DateTime.parse(json['implementedAt'] as String),
      status:
          $enumDecodeNullable(_$RecommendationStatusEnumMap, json['status']) ??
              RecommendationStatus.pending,
      sourceType: json['sourceType'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$RecommendationModelToJson(
        RecommendationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'farmId': instance.farmId,
      'cropId': instance.cropId,
      'type': _$RecommendationTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'priority': _$RecommendationPriorityEnumMap[instance.priority]!,
      'actions': instance.actions,
      'createdAt': instance.createdAt.toIso8601String(),
      'implementedAt': instance.implementedAt?.toIso8601String(),
      'status': _$RecommendationStatusEnumMap[instance.status]!,
      'sourceType': instance.sourceType,
      'metadata': instance.metadata,
    };

const _$RecommendationTypeEnumMap = {
  RecommendationType.fertilizer: 'fertilizer',
  RecommendationType.pesticide: 'pesticide',
  RecommendationType.irrigation: 'irrigation',
  RecommendationType.soilAmendment: 'soil_amendment',
  RecommendationType.cropManagement: 'crop_management',
  RecommendationType.diseaseControl: 'disease_control',
  RecommendationType.pestControl: 'pest_control',
  RecommendationType.harvest: 'harvest',
};

const _$RecommendationPriorityEnumMap = {
  RecommendationPriority.low: 'low',
  RecommendationPriority.medium: 'medium',
  RecommendationPriority.high: 'high',
  RecommendationPriority.critical: 'critical',
};

const _$RecommendationStatusEnumMap = {
  RecommendationStatus.pending: 'pending',
  RecommendationStatus.active: 'active',
  RecommendationStatus.completed: 'completed',
  RecommendationStatus.cancelled: 'cancelled',
  RecommendationStatus.expired: 'expired',
};

RecommendationAction _$RecommendationActionFromJson(
        Map<String, dynamic> json) =>
    RecommendationAction(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$ActionTypeEnumMap, json['type']),
      parameters: json['parameters'] as Map<String, dynamic>,
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      isCompleted: json['isCompleted'] as bool? ?? false,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$RecommendationActionToJson(
        RecommendationAction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$ActionTypeEnumMap[instance.type]!,
      'parameters': instance.parameters,
      'scheduledDate': instance.scheduledDate?.toIso8601String(),
      'isCompleted': instance.isCompleted,
      'completedAt': instance.completedAt?.toIso8601String(),
      'notes': instance.notes,
    };

const _$ActionTypeEnumMap = {
  ActionType.applyFertilizer: 'apply_fertilizer',
  ActionType.applyPesticide: 'apply_pesticide',
  ActionType.irrigate: 'irrigate',
  ActionType.soilTest: 'soil_test',
  ActionType.plant: 'plant',
  ActionType.harvest: 'harvest',
  ActionType.prune: 'prune',
  ActionType.monitor: 'monitor',
};
