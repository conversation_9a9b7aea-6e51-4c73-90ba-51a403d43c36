import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'smart_precision_agriculture.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        phone_number TEXT,
        profile_image_path TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Farms table
    await db.execute('''
      CREATE TABLE farms (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        address TEXT NOT NULL,
        city TEXT NOT NULL,
        state TEXT NOT NULL,
        country TEXT NOT NULL,
        postal_code TEXT,
        area REAL NOT NULL,
        area_unit TEXT DEFAULT 'hectares',
        farm_type TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Crops table
    await db.execute('''
      CREATE TABLE crops (
        id TEXT PRIMARY KEY,
        farm_id TEXT NOT NULL,
        name TEXT NOT NULL,
        variety TEXT NOT NULL,
        crop_type TEXT NOT NULL,
        planting_date TEXT NOT NULL,
        harvest_date TEXT,
        stage TEXT NOT NULL,
        area_allocated REAL NOT NULL,
        health_status TEXT DEFAULT 'good',
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (farm_id) REFERENCES farms (id)
      )
    ''');

    // Soil tests table
    await db.execute('''
      CREATE TABLE soil_tests (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        farm_id TEXT NOT NULL,
        sample_location TEXT NOT NULL,
        collection_date TEXT NOT NULL,
        analysis_date TEXT,
        status TEXT DEFAULT 'pending',
        ph_level REAL,
        nitrogen REAL,
        phosphorus REAL,
        potassium REAL,
        organic_matter REAL,
        calcium REAL,
        magnesium REAL,
        sulfur REAL,
        zinc REAL,
        iron REAL,
        manganese REAL,
        copper REAL,
        boron REAL,
        electrical_conductivity REAL,
        soil_texture TEXT,
        overall_health TEXT,
        recommendations TEXT,
        report_image_path TEXT,
        lab_name TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (farm_id) REFERENCES farms (id)
      )
    ''');

    // Leaf scans table
    await db.execute('''
      CREATE TABLE leaf_scans (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        farm_id TEXT NOT NULL,
        crop_id TEXT,
        image_path TEXT NOT NULL,
        scan_date TEXT NOT NULL,
        status TEXT DEFAULT 'processing',
        confidence REAL,
        overall_health TEXT,
        color_analysis TEXT,
        diseases_detected TEXT,
        deficiencies_detected TEXT,
        pests_detected TEXT,
        physical_condition TEXT,
        recommendations TEXT,
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (farm_id) REFERENCES farms (id),
        FOREIGN KEY (crop_id) REFERENCES crops (id)
      )
    ''');

    // Recommendations table
    await db.execute('''
      CREATE TABLE recommendations (
        id TEXT PRIMARY KEY,
        farm_id TEXT NOT NULL,
        crop_id TEXT,
        recommendation_type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        priority TEXT DEFAULT 'medium',
        status TEXT DEFAULT 'pending',
        source_type TEXT,
        source_id TEXT,
        actions TEXT,
        scheduled_date TEXT,
        implemented_date TEXT,
        notes TEXT,
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (farm_id) REFERENCES farms (id),
        FOREIGN KEY (crop_id) REFERENCES crops (id)
      )
    ''');

    // Weather data table
    await db.execute('''
      CREATE TABLE weather_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        farm_id TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        temperature REAL NOT NULL,
        humidity REAL NOT NULL,
        rainfall REAL DEFAULT 0.0,
        wind_speed REAL NOT NULL,
        wind_direction REAL,
        pressure REAL,
        condition TEXT NOT NULL,
        visibility REAL,
        uv_index REAL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (farm_id) REFERENCES farms (id)
      )
    ''');

    // Chat messages table
    await db.execute('''
      CREATE TABLE chat_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        message TEXT NOT NULL,
        response TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // User sessions table
    await db.execute('''
      CREATE TABLE user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        token TEXT NOT NULL,
        expires_at TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_users_email ON users(email)');
    await db.execute('CREATE INDEX idx_farms_user_id ON farms(user_id)');
    await db.execute('CREATE INDEX idx_crops_farm_id ON crops(farm_id)');
    await db.execute('CREATE INDEX idx_soil_tests_user_id ON soil_tests(user_id)');
    await db.execute('CREATE INDEX idx_soil_tests_farm_id ON soil_tests(farm_id)');
    await db.execute('CREATE INDEX idx_leaf_scans_user_id ON leaf_scans(user_id)');
    await db.execute('CREATE INDEX idx_leaf_scans_farm_id ON leaf_scans(farm_id)');
    await db.execute('CREATE INDEX idx_recommendations_farm_id ON recommendations(farm_id)');
    await db.execute('CREATE INDEX idx_weather_data_farm_id ON weather_data(farm_id)');
    await db.execute('CREATE INDEX idx_chat_messages_user_id ON chat_messages(user_id)');
    await db.execute('CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
    }
  }

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert(table, data);
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  Future<int> rawInsert(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawInsert(sql, arguments);
  }

  Future<int> rawUpdate(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawUpdate(sql, arguments);
  }

  Future<int> rawDelete(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawDelete(sql, arguments);
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  Future<void> deleteDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'smart_precision_agriculture.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
