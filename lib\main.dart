import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:go_router/go_router.dart';

import 'utils/constants.dart';
// import 'services/auth_service.dart';
import 'screens/welcome/welcome_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/home/<USER>';
import 'screens/soil_upload/soil_upload_screen.dart';
import 'screens/leaf_scan/leaf_scan_screen.dart';
import 'screens/recommendations/recommendations_screen.dart';
import 'screens/features/crop_optimization_screen.dart';
import 'screens/features/chatbot_screen.dart';
import 'database/database_helper.dart';
import 'services/sqlite_auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase (temporarily disabled)
  // await Firebase.initializeApp();

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(
    const ProviderScope(
      child: FarmManagementApp(),
    ),
  );
}

class FarmManagementApp extends ConsumerWidget {
  const FarmManagementApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp.router(
      title: 'Farm Management',
      debugShowCheckedModeBanner: false,
      theme: _buildTheme(),
      routerConfig: _router,
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      // fontFamily: 'Roboto',
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.textPrimary,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      cardTheme: const CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}

// Router configuration
final _router = GoRouter(
  initialLocation: '/',
  redirect: (context, state) async {
    // Check authentication for protected routes
    final authService = SqliteAuthService();
    final isLoggedIn = await authService.isLoggedIn();

    final isAuthRoute = state.matchedLocation.startsWith('/login') ||
        state.matchedLocation.startsWith('/register') ||
        state.matchedLocation == '/';

    // If not logged in and trying to access protected route, redirect to welcome
    if (!isLoggedIn && !isAuthRoute) {
      return '/';
    }

    // If logged in and trying to access auth routes, redirect to dashboard
    if (isLoggedIn && isAuthRoute && state.matchedLocation != '/') {
      return '/dashboard';
    }

    return null;
  },
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const WelcomeScreen(),
    ),
    GoRoute(
      path: '/login',
      name: 'login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/register',
      name: 'register',
      builder: (context, state) => const RegisterScreen(),
    ),
    GoRoute(
      path: '/dashboard',
      name: 'dashboard',
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: '/soil-upload',
      name: 'soil-upload',
      builder: (context, state) => const SoilUploadScreen(),
    ),
    GoRoute(
      path: '/leaf-scan',
      name: 'leaf-scan',
      builder: (context, state) => const LeafScanScreen(),
    ),
    GoRoute(
      path: '/recommendations',
      name: 'recommendations',
      builder: (context, state) => const RecommendationsScreen(),
    ),
    GoRoute(
      path: '/crop-optimization',
      name: 'crop-optimization',
      builder: (context, state) => const CropOptimizationScreen(),
    ),
    GoRoute(
      path: '/chatbot',
      name: 'chatbot',
      builder: (context, state) => const ChatbotScreen(),
    ),
    GoRoute(
      path: '/fertilizer-precision',
      name: 'fertilizer-precision',
      builder: (context, state) =>
          const CropOptimizationScreen(), // Placeholder
    ),
    GoRoute(
      path: '/weather-reports',
      name: 'weather-reports',
      builder: (context, state) =>
          const CropOptimizationScreen(), // Placeholder
    ),
    GoRoute(
      path: '/farm-map',
      name: 'farm-map',
      builder: (context, state) =>
          const CropOptimizationScreen(), // Placeholder
    ),
  ],
);
