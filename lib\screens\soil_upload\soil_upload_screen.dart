import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../utils/constants.dart';
import '../../utils/helpers.dart';
import '../../widgets/custom_button.dart';
import '../../models/soil_model.dart';

// State providers for soil upload
final soilUploadStateProvider = StateNotifierProvider<SoilUploadNotifier, SoilUploadState>((ref) {
  return SoilUploadNotifier();
});

class SoilUploadScreen extends ConsumerStatefulWidget {
  const SoilUploadScreen({super.key});

  @override
  ConsumerState<SoilUploadScreen> createState() => _SoilUploadScreenState();
}

class _SoilUploadScreenState extends ConsumerState<SoilUploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void dispose() {
    _locationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _pickImageFromCamera() async {
    try {
      // Check camera permission
      final cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        if (mounted) {
          AppHelpers.showErrorSnackBar(context, 'Camera permission is required');
        }
        return;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: AppConstants.imageQuality,
        maxWidth: AppConstants.maxImageWidth,
        maxHeight: AppConstants.maxImageHeight,
      );

      if (image != null) {
        ref.read(soilUploadStateProvider.notifier).setImage(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showErrorSnackBar(context, 'Failed to capture image: $e');
      }
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: AppConstants.imageQuality,
        maxWidth: AppConstants.maxImageWidth,
        maxHeight: AppConstants.maxImageHeight,
      );

      if (image != null) {
        ref.read(soilUploadStateProvider.notifier).setImage(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showErrorSnackBar(context, 'Failed to select image: $e');
      }
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppDimensions.radiusLarge)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Image Source',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingLarge),
              Row(
                children: [
                  Expanded(
                    child: _buildImageSourceOption(
                      context,
                      icon: Icons.camera_alt,
                      title: AppStrings.takePhoto,
                      onTap: () {
                        Navigator.pop(context);
                        _pickImageFromCamera();
                      },
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingMedium),
                  Expanded(
                    child: _buildImageSourceOption(
                      context,
                      icon: Icons.photo_library,
                      title: AppStrings.selectFromGallery,
                      onTap: () {
                        Navigator.pop(context);
                        _pickImageFromGallery();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _uploadSoilCard() async {
    if (!_formKey.currentState!.validate()) return;

    final state = ref.read(soilUploadStateProvider);
    if (state.selectedImage == null) {
      AppHelpers.showErrorSnackBar(context, 'Please select a soil health card image');
      return;
    }

    try {
      await ref.read(soilUploadStateProvider.notifier).uploadSoilCard(
        location: _locationController.text.trim(),
        notes: _notesController.text.trim(),
      );

      if (mounted) {
        AppHelpers.showSuccessSnackBar(context, 'Soil health card uploaded successfully');
        context.go('/dashboard');
      }
    } catch (e) {
      if (mounted) {
        AppHelpers.showErrorSnackBar(context, 'Failed to upload soil card: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(soilUploadStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.uploadSoilCard),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/dashboard'),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingMedium),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.landscape,
                        size: 48,
                        color: AppColors.primary,
                      ),
                      const SizedBox(height: AppDimensions.paddingMedium),
                      Text(
                        'Upload Soil Health Card',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppDimensions.paddingSmall),
                      Text(
                        'Take a photo or select an image of your soil health card for analysis',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppDimensions.paddingLarge),

              // Image Selection
              Text(
                'Soil Health Card Image',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: AppDimensions.paddingMedium),

              _buildImageSection(state),

              const SizedBox(height: AppDimensions.paddingLarge),

              // Location Field
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(
                  labelText: 'Sample Location',
                  hintText: 'e.g., North Field, Plot A',
                  prefixIcon: Icon(Icons.location_on_outlined),
                ),
                validator: (value) => AppHelpers.validateRequired(value, fieldName: 'Location'),
              ),

              const SizedBox(height: AppDimensions.paddingMedium),

              // Notes Field
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  hintText: 'Additional information about the soil sample',
                  prefixIcon: Icon(Icons.notes_outlined),
                ),
              ),

              const SizedBox(height: AppDimensions.paddingXLarge),

              // Upload Button
              PrimaryButton(
                text: 'Upload & Analyze',
                icon: Icons.upload,
                isLoading: state.isUploading,
                onPressed: state.isUploading ? null : _uploadSoilCard,
              ),

              const SizedBox(height: AppDimensions.paddingMedium),

              // Help Text
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                  border: Border.all(color: AppColors.info.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.info_outline, color: AppColors.info),
                    const SizedBox(width: AppDimensions.paddingSmall),
                    Expanded(
                      child: Text(
                        'Ensure the soil health card is clearly visible and well-lit for accurate analysis.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.info,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection(SoilUploadState state) {
    if (state.selectedImage != null) {
      return Card(
        child: Column(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(AppDimensions.radiusMedium),
              ),
              child: Image.file(
                state.selectedImage!,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingMedium),
              child: Row(
                children: [
                  Expanded(
                    child: SecondaryButton(
                      text: 'Change Image',
                      icon: Icons.edit,
                      onPressed: _showImageSourceDialog,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingSmall),
                  IconButton(
                    onPressed: () {
                      ref.read(soilUploadStateProvider.notifier).removeImage();
                    },
                    icon: const Icon(Icons.delete, color: AppColors.error),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return Card(
      child: InkWell(
        onTap: _showImageSourceDialog,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        child: Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.border,
              style: BorderStyle.solid,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_photo_alternate_outlined,
                size: 64,
                color: AppColors.textSecondary.withOpacity(0.7),
              ),
              const SizedBox(height: AppDimensions.paddingMedium),
              Text(
                'Tap to add soil health card image',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingSmall),
              Text(
                'Camera or Gallery',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSourceOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                ),
                child: Icon(
                  icon,
                  size: AppDimensions.iconLarge,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: AppDimensions.paddingMedium),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// State management for soil upload
class SoilUploadState {
  final File? selectedImage;
  final bool isUploading;
  final String? error;
  final SoilModel? uploadedSoil;

  const SoilUploadState({
    this.selectedImage,
    this.isUploading = false,
    this.error,
    this.uploadedSoil,
  });

  SoilUploadState copyWith({
    File? selectedImage,
    bool? isUploading,
    String? error,
    SoilModel? uploadedSoil,
  }) {
    return SoilUploadState(
      selectedImage: selectedImage ?? this.selectedImage,
      isUploading: isUploading ?? this.isUploading,
      error: error ?? this.error,
      uploadedSoil: uploadedSoil ?? this.uploadedSoil,
    );
  }
}

class SoilUploadNotifier extends StateNotifier<SoilUploadState> {
  SoilUploadNotifier() : super(const SoilUploadState());

  void setImage(File image) {
    state = state.copyWith(selectedImage: image, error: null);
  }

  void removeImage() {
    state = state.copyWith(selectedImage: null);
  }

  Future<void> uploadSoilCard({
    required String location,
    String? notes,
  }) async {
    if (state.selectedImage == null) {
      throw Exception('No image selected');
    }

    state = state.copyWith(isUploading: true, error: null);

    try {
      // TODO: Implement actual upload logic
      // This would typically involve:
      // 1. Compressing the image
      // 2. Uploading to cloud storage
      // 3. Sending metadata to backend
      // 4. Processing with OCR/ML services

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 2));

      // Create mock soil model
      final soilModel = SoilModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        farmId: 'mock-farm-id',
        sampleLocation: location,
        collectionDate: DateTime.now(),
        testResults: const SoilTestResults(
          phLevel: SoilParameter(
            name: 'pH Level',
            value: 6.5,
            unit: 'pH',
            optimalMin: 6.0,
            optimalMax: 7.5,
            status: SoilParameterStatus.optimal,
          ),
          nitrogen: SoilParameter(
            name: 'Nitrogen',
            value: 250.0,
            unit: 'kg/ha',
            optimalMin: 280.0,
            optimalMax: 400.0,
            status: SoilParameterStatus.deficient,
          ),
          phosphorus: SoilParameter(
            name: 'Phosphorus',
            value: 15.0,
            unit: 'kg/ha',
            optimalMin: 11.0,
            optimalMax: 25.0,
            status: SoilParameterStatus.optimal,
          ),
          potassium: SoilParameter(
            name: 'Potassium',
            value: 180.0,
            unit: 'kg/ha',
            optimalMin: 120.0,
            optimalMax: 280.0,
            status: SoilParameterStatus.optimal,
          ),
          organicMatter: SoilParameter(
            name: 'Organic Matter',
            value: 2.8,
            unit: '%',
            optimalMin: 2.5,
            optimalMax: 4.0,
            status: SoilParameterStatus.optimal,
          ),
        ),
        recommendations: const SoilRecommendations(
          fertilizers: [],
          amendments: [],
          generalAdvice: ['Apply nitrogen fertilizer', 'Monitor soil moisture'],
          cropSuitability: CropSuitability(
            suitableCrops: [],
            moderatelySuitableCrops: [],
            unsuitableCrops: [],
          ),
          nextTestingDate: null,
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      state = state.copyWith(
        isUploading: false,
        uploadedSoil: soilModel,
      );
    } catch (e) {
      state = state.copyWith(
        isUploading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  void reset() {
    state = const SoilUploadState();
  }
}
