import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/user_model.dart';
import '../models/farm_model.dart';
import '../models/soil_model.dart';
import '../models/leaf_scan_model.dart';
import '../models/recommendation_model.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

// Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

// Handles HTTP requests to backend
class ApiService {
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: Duration(milliseconds: AppConstants.receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token to requests
        final token = await _getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          // Token expired, try to refresh
          final newToken = await _refreshAuthToken();
          if (newToken != null) {
            // Retry the request with new token
            final options = error.requestOptions;
            options.headers['Authorization'] = 'Bearer $newToken';
            try {
              final response = await _dio.fetch(options);
              handler.resolve(response);
              return;
            } catch (e) {
              // If retry fails, continue with original error
            }
          }
        }
        handler.next(error);
      },
    ));
  }

  // User Management
  Future<UserModel> getUserProfile(String uid) async {
    try {
      final response = await _dio.get('/users/$uid');
      return UserModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<UserModel> createUserProfile({
    required String uid,
    required String email,
    required String firstName,
    required String lastName,
    String? phoneNumber,
  }) async {
    try {
      final response = await _dio.post('/users', data: {
        'uid': uid,
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
        'phoneNumber': phoneNumber,
      });
      return UserModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<UserModel> updateUserProfile({
    required String uid,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (firstName != null) data['firstName'] = firstName;
      if (lastName != null) data['lastName'] = lastName;
      if (phoneNumber != null) data['phoneNumber'] = phoneNumber;
      if (profileImageUrl != null) data['profileImageUrl'] = profileImageUrl;

      final response = await _dio.put('/users/$uid', data: data);
      return UserModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> deleteUserProfile(String uid) async {
    try {
      await _dio.delete('/users/$uid');
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Farm Management
  Future<List<FarmModel>> getUserFarms(String userId) async {
    try {
      final response = await _dio.get('/farms', queryParameters: {
        'userId': userId,
      });
      return (response.data as List)
          .map((farm) => FarmModel.fromJson(farm))
          .toList();
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<FarmModel> getFarm(String farmId) async {
    try {
      final response = await _dio.get('/farms/$farmId');
      return FarmModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<FarmModel> createFarm(Map<String, dynamic> farmData) async {
    try {
      final response = await _dio.post('/farms', data: farmData);
      return FarmModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<FarmModel> updateFarm(String farmId, Map<String, dynamic> farmData) async {
    try {
      final response = await _dio.put('/farms/$farmId', data: farmData);
      return FarmModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<void> deleteFarm(String farmId) async {
    try {
      await _dio.delete('/farms/$farmId');
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  // Helper methods
  Future<String?> _getAuthToken() async {
    // This would typically get the token from SharedPreferences
    // For now, we'll return null and let the auth service handle it
    return null;
  }

  Future<String?> _refreshAuthToken() async {
    // This would refresh the Firebase token
    // For now, we'll return null
    return null;
  }
}

// API Exception class
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? code;

  ApiException({
    required this.message,
    this.statusCode,
    this.code,
  });

  factory ApiException.fromDioError(DioException error) {
    String message = 'An unexpected error occurred';
    int? statusCode = error.response?.statusCode;
    String? code;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Send timeout';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Receive timeout';
        break;
      case DioExceptionType.badResponse:
        final data = error.response?.data;
        if (data is Map<String, dynamic>) {
          message = data['message'] ?? message;
          code = data['code'];
        }
        break;
      case DioExceptionType.cancel:
        message = 'Request cancelled';
        break;
      case DioExceptionType.connectionError:
        message = 'Connection error';
        break;
      case DioExceptionType.unknown:
        message = 'Unknown error occurred';
        break;
      default:
        message = error.message ?? message;
    }

    return ApiException(
      message: message,
      statusCode: statusCode,
      code: code,
    );
  }

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode, Code: $code)';
  }
}
