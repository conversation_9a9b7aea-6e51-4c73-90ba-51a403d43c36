import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../database/database_helper.dart';

class SoilTest {
  final String id;
  final String userId;
  final String farmId;
  final String sampleLocation;
  final DateTime collectionDate;
  final DateTime? analysisDate;
  final String status;
  final double? phLevel;
  final double? nitrogen;
  final double? phosphorus;
  final double? potassium;
  final double? organicMatter;
  final double? calcium;
  final double? magnesium;
  final double? sulfur;
  final double? zinc;
  final double? iron;
  final double? manganese;
  final double? copper;
  final double? boron;
  final double? electricalConductivity;
  final String? soilTexture;
  final String? overallHealth;
  final String? recommendations;
  final String? reportImagePath;
  final String? labName;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  SoilTest({
    required this.id,
    required this.userId,
    required this.farmId,
    required this.sampleLocation,
    required this.collectionDate,
    this.analysisDate,
    this.status = 'pending',
    this.phLevel,
    this.nitrogen,
    this.phosphorus,
    this.potassium,
    this.organicMatter,
    this.calcium,
    this.magnesium,
    this.sulfur,
    this.zinc,
    this.iron,
    this.manganese,
    this.copper,
    this.boron,
    this.electricalConductivity,
    this.soilTexture,
    this.overallHealth,
    this.recommendations,
    this.reportImagePath,
    this.labName,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'farm_id': farmId,
      'sample_location': sampleLocation,
      'collection_date': collectionDate.toIso8601String(),
      'analysis_date': analysisDate?.toIso8601String(),
      'status': status,
      'ph_level': phLevel,
      'nitrogen': nitrogen,
      'phosphorus': phosphorus,
      'potassium': potassium,
      'organic_matter': organicMatter,
      'calcium': calcium,
      'magnesium': magnesium,
      'sulfur': sulfur,
      'zinc': zinc,
      'iron': iron,
      'manganese': manganese,
      'copper': copper,
      'boron': boron,
      'electrical_conductivity': electricalConductivity,
      'soil_texture': soilTexture,
      'overall_health': overallHealth,
      'recommendations': recommendations,
      'report_image_path': reportImagePath,
      'lab_name': labName,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory SoilTest.fromMap(Map<String, dynamic> map) {
    return SoilTest(
      id: map['id'] ?? '',
      userId: map['user_id'] ?? '',
      farmId: map['farm_id'] ?? '',
      sampleLocation: map['sample_location'] ?? '',
      collectionDate: DateTime.parse(map['collection_date']),
      analysisDate: map['analysis_date'] != null ? DateTime.parse(map['analysis_date']) : null,
      status: map['status'] ?? 'pending',
      phLevel: map['ph_level']?.toDouble(),
      nitrogen: map['nitrogen']?.toDouble(),
      phosphorus: map['phosphorus']?.toDouble(),
      potassium: map['potassium']?.toDouble(),
      organicMatter: map['organic_matter']?.toDouble(),
      calcium: map['calcium']?.toDouble(),
      magnesium: map['magnesium']?.toDouble(),
      sulfur: map['sulfur']?.toDouble(),
      zinc: map['zinc']?.toDouble(),
      iron: map['iron']?.toDouble(),
      manganese: map['manganese']?.toDouble(),
      copper: map['copper']?.toDouble(),
      boron: map['boron']?.toDouble(),
      electricalConductivity: map['electrical_conductivity']?.toDouble(),
      soilTexture: map['soil_texture'],
      overallHealth: map['overall_health'],
      recommendations: map['recommendations'],
      reportImagePath: map['report_image_path'],
      labName: map['lab_name'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }
}

class SoilService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Generate unique ID
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return '${timestamp}_$random';
  }

  // Create a new soil test
  Future<SoilTest> createSoilTest({
    required String userId,
    required String farmId,
    required String sampleLocation,
    required DateTime collectionDate,
    String? reportImagePath,
    String? labName,
    String? notes,
  }) async {
    final soilTest = SoilTest(
      id: _generateId(),
      userId: userId,
      farmId: farmId,
      sampleLocation: sampleLocation,
      collectionDate: collectionDate,
      reportImagePath: reportImagePath,
      labName: labName,
      notes: notes,
      createdAt: DateTime.now(),
    );

    await _dbHelper.insert('soil_tests', soilTest.toMap());
    return soilTest;
  }

  // Analyze soil test (simulate analysis)
  Future<SoilTest> analyzeSoilTest(String soilTestId) async {
    // Simulate analysis with random but realistic values
    final random = Random();
    
    final analysisData = {
      'analysis_date': DateTime.now().toIso8601String(),
      'status': 'completed',
      'ph_level': 6.0 + random.nextDouble() * 2.0, // 6.0 - 8.0
      'nitrogen': 20 + random.nextDouble() * 60, // 20 - 80 ppm
      'phosphorus': 10 + random.nextDouble() * 40, // 10 - 50 ppm
      'potassium': 100 + random.nextDouble() * 300, // 100 - 400 ppm
      'organic_matter': 2.0 + random.nextDouble() * 4.0, // 2.0 - 6.0%
      'calcium': 800 + random.nextDouble() * 1200, // 800 - 2000 ppm
      'magnesium': 100 + random.nextDouble() * 300, // 100 - 400 ppm
      'sulfur': 10 + random.nextDouble() * 30, // 10 - 40 ppm
      'zinc': 1.0 + random.nextDouble() * 4.0, // 1.0 - 5.0 ppm
      'iron': 10 + random.nextDouble() * 40, // 10 - 50 ppm
      'manganese': 5 + random.nextDouble() * 20, // 5 - 25 ppm
      'copper': 0.5 + random.nextDouble() * 2.0, // 0.5 - 2.5 ppm
      'boron': 0.5 + random.nextDouble() * 1.5, // 0.5 - 2.0 ppm
      'electrical_conductivity': 0.5 + random.nextDouble() * 2.0, // 0.5 - 2.5 dS/m
      'soil_texture': ['Sandy', 'Loamy', 'Clay', 'Silty'][random.nextInt(4)],
      'updated_at': DateTime.now().toIso8601String(),
    };

    // Generate overall health and recommendations based on values
    final phLevel = analysisData['ph_level'] as double;
    final nitrogen = analysisData['nitrogen'] as double;
    final phosphorus = analysisData['phosphorus'] as double;
    final potassium = analysisData['potassium'] as double;

    String overallHealth;
    String recommendations;

    if (phLevel >= 6.0 && phLevel <= 7.5 && nitrogen >= 40 && phosphorus >= 25 && potassium >= 200) {
      overallHealth = 'Excellent';
      recommendations = 'Your soil is in excellent condition. Continue with current management practices and regular monitoring.';
    } else if (phLevel >= 5.5 && phLevel <= 8.0 && nitrogen >= 25 && phosphorus >= 15 && potassium >= 150) {
      overallHealth = 'Good';
      recommendations = 'Soil condition is good. Consider minor adjustments to nutrient levels for optimal crop growth.';
    } else if (phLevel >= 5.0 && phLevel <= 8.5 && nitrogen >= 15 && phosphorus >= 10 && potassium >= 100) {
      overallHealth = 'Fair';
      recommendations = 'Soil needs improvement. Consider adding organic matter and adjusting pH levels. Regular fertilization recommended.';
    } else {
      overallHealth = 'Poor';
      recommendations = 'Soil requires significant improvement. Consider soil amendments, pH correction, and comprehensive fertilization program.';
    }

    analysisData['overall_health'] = overallHealth;
    analysisData['recommendations'] = recommendations;

    await _dbHelper.update(
      'soil_tests',
      analysisData,
      where: 'id = ?',
      whereArgs: [soilTestId],
    );

    return await getSoilTestById(soilTestId) ?? throw Exception('Soil test not found');
  }

  // Get soil tests for a user
  Future<List<SoilTest>> getSoilTestsForUser(String userId) async {
    final testMaps = await _dbHelper.query(
      'soil_tests',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return testMaps.map((map) => SoilTest.fromMap(map)).toList();
  }

  // Get soil tests for a farm
  Future<List<SoilTest>> getSoilTestsForFarm(String farmId) async {
    final testMaps = await _dbHelper.query(
      'soil_tests',
      where: 'farm_id = ?',
      whereArgs: [farmId],
      orderBy: 'created_at DESC',
    );

    return testMaps.map((map) => SoilTest.fromMap(map)).toList();
  }

  // Get soil test by ID
  Future<SoilTest?> getSoilTestById(String testId) async {
    final testMaps = await _dbHelper.query(
      'soil_tests',
      where: 'id = ?',
      whereArgs: [testId],
    );

    if (testMaps.isNotEmpty) {
      return SoilTest.fromMap(testMaps.first);
    }
    return null;
  }

  // Update soil test
  Future<SoilTest?> updateSoilTest({
    required String testId,
    String? sampleLocation,
    DateTime? collectionDate,
    String? status,
    String? reportImagePath,
    String? labName,
    String? notes,
  }) async {
    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (sampleLocation != null) updateData['sample_location'] = sampleLocation;
    if (collectionDate != null) updateData['collection_date'] = collectionDate.toIso8601String();
    if (status != null) updateData['status'] = status;
    if (reportImagePath != null) updateData['report_image_path'] = reportImagePath;
    if (labName != null) updateData['lab_name'] = labName;
    if (notes != null) updateData['notes'] = notes;

    final rowsAffected = await _dbHelper.update(
      'soil_tests',
      updateData,
      where: 'id = ?',
      whereArgs: [testId],
    );

    if (rowsAffected > 0) {
      return await getSoilTestById(testId);
    }
    return null;
  }

  // Delete soil test
  Future<bool> deleteSoilTest(String testId) async {
    final rowsAffected = await _dbHelper.delete(
      'soil_tests',
      where: 'id = ?',
      whereArgs: [testId],
    );

    return rowsAffected > 0;
  }
}

// Provider for the soil service
final soilServiceProvider = Provider<SoilService>((ref) {
  return SoilService();
});

// Provider for user soil tests
final userSoilTestsProvider = FutureProvider.family<List<SoilTest>, String>((ref, userId) async {
  final soilService = ref.read(soilServiceProvider);
  return await soilService.getSoilTestsForUser(userId);
});

// Provider for farm soil tests
final farmSoilTestsProvider = FutureProvider.family<List<SoilTest>, String>((ref, farmId) async {
  final soilService = ref.read(soilServiceProvider);
  return await soilService.getSoilTestsForFarm(farmId);
});
