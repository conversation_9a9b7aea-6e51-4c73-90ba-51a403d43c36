import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../database/database_helper.dart';
import '../models/sqlite_user_model.dart';
import '../utils/constants.dart';

class AuthResult {
  final bool isSuccess;
  final String? error;
  final SqliteUser? user;
  final String? token;

  AuthResult({
    required this.isSuccess,
    this.error,
    this.user,
    this.token,
  });

  factory AuthResult.success({SqliteUser? user, String? token}) {
    return AuthResult(
      isSuccess: true,
      user: user,
      token: token,
    );
  }

  factory AuthResult.failure(String error) {
    return AuthResult(
      isSuccess: false,
      error: error,
    );
  }
}

class SqliteAuthService {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Hash password using SHA-256
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Generate random token
  String _generateToken() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  // Generate unique ID
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return '${timestamp}_$random';
  }

  // Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phoneNumber,
  }) async {
    try {
      // Check if user already exists
      final existingUsers = await _dbHelper.query(
        'users',
        where: 'email = ?',
        whereArgs: [email.toLowerCase()],
      );

      if (existingUsers.isNotEmpty) {
        return AuthResult.failure('Email already registered');
      }

      // Create new user
      final userId = _generateId();
      final passwordHash = _hashPassword(password);
      final now = DateTime.now();

      final user = SqliteUser(
        id: userId,
        email: email.toLowerCase(),
        passwordHash: passwordHash,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        createdAt: now,
      );

      // Insert user into database
      await _dbHelper.insert('users', user.toMap());

      // Create session
      final token = _generateToken();
      final session = UserSession(
        id: _generateId(),
        userId: userId,
        token: token,
        expiresAt: now.add(const Duration(days: 30)),
        createdAt: now,
      );

      await _dbHelper.insert('user_sessions', session.toMap());

      // Store session in SharedPreferences
      await _storeSession(session);

      return AuthResult.success(user: user, token: token);
    } catch (e) {
      return AuthResult.failure('Registration failed: ${e.toString()}');
    }
  }

  // Login user
  Future<AuthResult> login({
    required String email,
    required String password,
  }) async {
    try {
      // Find user by email
      final users = await _dbHelper.query(
        'users',
        where: 'email = ? AND is_active = 1',
        whereArgs: [email.toLowerCase()],
      );

      if (users.isEmpty) {
        return AuthResult.failure('Invalid email or password');
      }

      final userMap = users.first;
      final user = SqliteUser.fromMap(userMap);

      // Verify password
      final passwordHash = _hashPassword(password);
      if (user.passwordHash != passwordHash) {
        return AuthResult.failure('Invalid email or password');
      }

      // Create new session
      final token = _generateToken();
      final now = DateTime.now();
      final session = UserSession(
        id: _generateId(),
        userId: user.id,
        token: token,
        expiresAt: now.add(const Duration(days: 30)),
        createdAt: now,
      );

      // Clean up old sessions
      await _dbHelper.delete(
        'user_sessions',
        where: 'user_id = ? OR expires_at < ?',
        whereArgs: [user.id, now.toIso8601String()],
      );

      // Insert new session
      await _dbHelper.insert('user_sessions', session.toMap());

      // Store session in SharedPreferences
      await _storeSession(session);

      return AuthResult.success(user: user, token: token);
    } catch (e) {
      return AuthResult.failure('Login failed: ${e.toString()}');
    }
  }

  // Get current user
  Future<SqliteUser?> getCurrentUser() async {
    try {
      final session = await _getCurrentSession();
      if (session == null || session.isExpired) {
        await logout();
        return null;
      }

      final users = await _dbHelper.query(
        'users',
        where: 'id = ? AND is_active = 1',
        whereArgs: [session.userId],
      );

      if (users.isEmpty) {
        await logout();
        return null;
      }

      return SqliteUser.fromMap(users.first);
    } catch (e) {
      return null;
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final user = await getCurrentUser();
    return user != null;
  }

  // Logout user
  Future<void> logout() async {
    try {
      final session = await _getCurrentSession();
      if (session != null) {
        // Delete session from database
        await _dbHelper.delete(
          'user_sessions',
          where: 'id = ?',
          whereArgs: [session.id],
        );
      }

      // Clear SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.authTokenKey);
      await prefs.remove('user_session');
    } catch (e) {
      // Ignore errors during logout
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile({
    required String userId,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImagePath,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (firstName != null) updateData['first_name'] = firstName;
      if (lastName != null) updateData['last_name'] = lastName;
      if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
      if (profileImagePath != null) updateData['profile_image_path'] = profileImagePath;

      final rowsAffected = await _dbHelper.update(
        'users',
        updateData,
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (rowsAffected == 0) {
        return AuthResult.failure('User not found');
      }

      // Get updated user
      final users = await _dbHelper.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (users.isNotEmpty) {
        final user = SqliteUser.fromMap(users.first);
        return AuthResult.success(user: user);
      }

      return AuthResult.failure('Failed to retrieve updated user');
    } catch (e) {
      return AuthResult.failure('Update failed: ${e.toString()}');
    }
  }

  // Change password
  Future<AuthResult> changePassword({
    required String userId,
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // Verify current password
      final users = await _dbHelper.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (users.isEmpty) {
        return AuthResult.failure('User not found');
      }

      final user = SqliteUser.fromMap(users.first);
      final currentPasswordHash = _hashPassword(currentPassword);

      if (user.passwordHash != currentPasswordHash) {
        return AuthResult.failure('Current password is incorrect');
      }

      // Update password
      final newPasswordHash = _hashPassword(newPassword);
      final rowsAffected = await _dbHelper.update(
        'users',
        {
          'password_hash': newPasswordHash,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (rowsAffected == 0) {
        return AuthResult.failure('Failed to update password');
      }

      // Invalidate all sessions except current
      final currentSession = await _getCurrentSession();
      if (currentSession != null) {
        await _dbHelper.delete(
          'user_sessions',
          where: 'user_id = ? AND id != ?',
          whereArgs: [userId, currentSession.id],
        );
      }

      return AuthResult.success();
    } catch (e) {
      return AuthResult.failure('Password change failed: ${e.toString()}');
    }
  }

  // Private helper methods
  Future<void> _storeSession(UserSession session) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.authTokenKey, session.token);
    await prefs.setString('user_session', session.toJson());
  }

  Future<UserSession?> _getCurrentSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionJson = prefs.getString('user_session');
      if (sessionJson == null) return null;

      final session = UserSession.fromJson(sessionJson);
      if (session.isExpired) {
        await logout();
        return null;
      }

      return session;
    } catch (e) {
      return null;
    }
  }
}

// Provider for the auth service
final sqliteAuthServiceProvider = Provider<SqliteAuthService>((ref) {
  return SqliteAuthService();
});

// Provider for current user
final currentUserProvider = FutureProvider<SqliteUser?>((ref) async {
  final authService = ref.read(sqliteAuthServiceProvider);
  return await authService.getCurrentUser();
});

// Provider for auth state
final authStateProvider = StreamProvider<SqliteUser?>((ref) async* {
  final authService = ref.read(sqliteAuthServiceProvider);
  
  // Initial state
  yield await authService.getCurrentUser();
  
  // Listen for changes (simplified - in a real app you might use a stream)
  while (true) {
    await Future.delayed(const Duration(seconds: 30));
    yield await authService.getCurrentUser();
  }
});
