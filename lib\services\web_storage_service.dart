import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/sqlite_user_model.dart';
import '../models/sqlite_farm_model.dart';
import '../models/sqlite_crop_model.dart';
import '../models/soil_test_model.dart';

/// Web-compatible storage service using Hive
class WebStorageService {
  static const String _usersBox = 'users';
  static const String _farmsBox = 'farms';
  static const String _cropsBox = 'crops';
  static const String _soilTestsBox = 'soil_tests';
  static const String _sessionsBox = 'sessions';

  static WebStorageService? _instance;
  static WebStorageService get instance => _instance ??= WebStorageService._();
  
  WebStorageService._();

  // Initialize Hive boxes
  Future<void> initialize() async {
    if (!kIsWeb) return; // Only for web
    
    try {
      await Hive.openBox(_usersBox);
      await Hive.openBox(_farmsBox);
      await Hive.openBox(_cropsBox);
      await Hive.openBox(_soilTestsBox);
      await Hive.openBox(_sessionsBox);
    } catch (e) {
      print('Error initializing web storage: $e');
    }
  }

  // User operations
  Future<SqliteUser?> getUserByEmail(String email) async {
    if (!kIsWeb) return null;
    
    try {
      final box = Hive.box(_usersBox);
      final userData = box.get(email);
      if (userData != null) {
        return SqliteUser.fromMap(Map<String, dynamic>.from(userData));
      }
    } catch (e) {
      print('Error getting user: $e');
    }
    return null;
  }

  Future<bool> createUser(SqliteUser user) async {
    if (!kIsWeb) return false;
    
    try {
      final box = Hive.box(_usersBox);
      await box.put(user.email, user.toMap());
      return true;
    } catch (e) {
      print('Error creating user: $e');
      return false;
    }
  }

  Future<SqliteUser?> getUserById(String id) async {
    if (!kIsWeb) return null;
    
    try {
      final box = Hive.box(_usersBox);
      for (final userData in box.values) {
        final user = SqliteUser.fromMap(Map<String, dynamic>.from(userData));
        if (user.id == id) return user;
      }
    } catch (e) {
      print('Error getting user by ID: $e');
    }
    return null;
  }

  // Session operations
  Future<void> saveSession(String userId, String token) async {
    if (!kIsWeb) return;
    
    try {
      final box = Hive.box(_sessionsBox);
      await box.put('current_session', {
        'userId': userId,
        'token': token,
        'createdAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error saving session: $e');
    }
  }

  Future<Map<String, String>?> getCurrentSession() async {
    if (!kIsWeb) return null;
    
    try {
      final box = Hive.box(_sessionsBox);
      final sessionData = box.get('current_session');
      if (sessionData != null) {
        return {
          'userId': sessionData['userId'],
          'token': sessionData['token'],
        };
      }
    } catch (e) {
      print('Error getting session: $e');
    }
    return null;
  }

  Future<void> clearSession() async {
    if (!kIsWeb) return;
    
    try {
      final box = Hive.box(_sessionsBox);
      await box.delete('current_session');
    } catch (e) {
      print('Error clearing session: $e');
    }
  }

  // Farm operations
  Future<bool> createFarm(SqliteFarm farm) async {
    if (!kIsWeb) return false;
    
    try {
      final box = Hive.box(_farmsBox);
      await box.put(farm.id, farm.toMap());
      return true;
    } catch (e) {
      print('Error creating farm: $e');
      return false;
    }
  }

  Future<List<SqliteFarm>> getFarmsForUser(String userId) async {
    if (!kIsWeb) return [];
    
    try {
      final box = Hive.box(_farmsBox);
      final farms = <SqliteFarm>[];
      
      for (final farmData in box.values) {
        final farm = SqliteFarm.fromMap(Map<String, dynamic>.from(farmData));
        if (farm.userId == userId) {
          farms.add(farm);
        }
      }
      
      return farms;
    } catch (e) {
      print('Error getting farms: $e');
      return [];
    }
  }

  // Crop operations
  Future<bool> createCrop(SqliteCrop crop) async {
    if (!kIsWeb) return false;
    
    try {
      final box = Hive.box(_cropsBox);
      await box.put(crop.id, crop.toMap());
      return true;
    } catch (e) {
      print('Error creating crop: $e');
      return false;
    }
  }

  Future<List<SqliteCrop>> getCropsForFarm(String farmId) async {
    if (!kIsWeb) return [];
    
    try {
      final box = Hive.box(_cropsBox);
      final crops = <SqliteCrop>[];
      
      for (final cropData in box.values) {
        final crop = SqliteCrop.fromMap(Map<String, dynamic>.from(cropData));
        if (crop.farmId == farmId) {
          crops.add(crop);
        }
      }
      
      return crops;
    } catch (e) {
      print('Error getting crops: $e');
      return [];
    }
  }

  // Soil test operations
  Future<bool> createSoilTest(SoilTest soilTest) async {
    if (!kIsWeb) return false;
    
    try {
      final box = Hive.box(_soilTestsBox);
      await box.put(soilTest.id, soilTest.toMap());
      return true;
    } catch (e) {
      print('Error creating soil test: $e');
      return false;
    }
  }

  Future<bool> updateSoilTest(SoilTest soilTest) async {
    if (!kIsWeb) return false;
    
    try {
      final box = Hive.box(_soilTestsBox);
      await box.put(soilTest.id, soilTest.toMap());
      return true;
    } catch (e) {
      print('Error updating soil test: $e');
      return false;
    }
  }

  Future<List<SoilTest>> getSoilTestsForUser(String userId) async {
    if (!kIsWeb) return [];
    
    try {
      final box = Hive.box(_soilTestsBox);
      final soilTests = <SoilTest>[];
      
      for (final testData in box.values) {
        final test = SoilTest.fromMap(Map<String, dynamic>.from(testData));
        if (test.userId == userId) {
          soilTests.add(test);
        }
      }
      
      return soilTests;
    } catch (e) {
      print('Error getting soil tests: $e');
      return [];
    }
  }

  Future<List<SoilTest>> getSoilTestsForFarm(String farmId) async {
    if (!kIsWeb) return [];
    
    try {
      final box = Hive.box(_soilTestsBox);
      final soilTests = <SoilTest>[];
      
      for (final testData in box.values) {
        final test = SoilTest.fromMap(Map<String, dynamic>.from(testData));
        if (test.farmId == farmId) {
          soilTests.add(test);
        }
      }
      
      return soilTests;
    } catch (e) {
      print('Error getting soil tests for farm: $e');
      return [];
    }
  }

  Future<SoilTest?> getSoilTestById(String id) async {
    if (!kIsWeb) return null;
    
    try {
      final box = Hive.box(_soilTestsBox);
      final testData = box.get(id);
      if (testData != null) {
        return SoilTest.fromMap(Map<String, dynamic>.from(testData));
      }
    } catch (e) {
      print('Error getting soil test by ID: $e');
    }
    return null;
  }

  // Clear all data (for testing)
  Future<void> clearAllData() async {
    if (!kIsWeb) return;
    
    try {
      await Hive.box(_usersBox).clear();
      await Hive.box(_farmsBox).clear();
      await Hive.box(_cropsBox).clear();
      await Hive.box(_soilTestsBox).clear();
      await Hive.box(_sessionsBox).clear();
    } catch (e) {
      print('Error clearing data: $e');
    }
  }
}
