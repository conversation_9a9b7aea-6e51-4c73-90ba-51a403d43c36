import 'package:flutter/material.dart';

// App-wide constants (colors, strings, etc.)

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2E7D32); // Green for agriculture
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF1B5E20);

  // Secondary Colors
  static const Color secondary = Color(0xFF8BC34A); // Light green
  static const Color accent = Color(0xFFFF9800); // Orange for alerts

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textLight = Color(0xFFBDBDBD);

  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFFFFFFF);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Border and Divider
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFBDBDBD);

  // Soil Health Colors
  static const Color soilExcellent = Color(0xFF4CAF50);
  static const Color soilGood = Color(0xFF8BC34A);
  static const Color soilFair = Color(0xFFFF9800);
  static const Color soilPoor = Color(0xFFF44336);
}

class AppStrings {
  // App Info
  static const String appName = 'Farm Management';
  static const String appVersion = '1.0.0';

  // Authentication
  static const String login = 'Login';
  static const String register = 'Register';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String forgotPassword = 'Forgot Password?';
  static const String dontHaveAccount = "Don't have an account?";
  static const String alreadyHaveAccount = 'Already have an account?';
  static const String signUp = 'Sign Up';
  static const String signIn = 'Sign In';
  static const String logout = 'Logout';

  // Dashboard
  static const String dashboard = 'Dashboard';
  static const String welcome = 'Welcome';
  static const String farmOverview = 'Farm Overview';
  static const String quickActions = 'Quick Actions';
  static const String recentActivity = 'Recent Activity';

  // Features
  static const String soilHealth = 'Soil Health';
  static const String leafScan = 'Leaf Scan';
  static const String recommendations = 'Recommendations';
  static const String weatherInfo = 'Weather Info';
  static const String farmMap = 'Farm Map';

  // Soil Health
  static const String uploadSoilCard = 'Upload Soil Health Card';
  static const String soilAnalysis = 'Soil Analysis';
  static const String soilReport = 'Soil Report';
  static const String takePhoto = 'Take Photo';
  static const String selectFromGallery = 'Select from Gallery';

  // Leaf Scanning
  static const String scanLeaf = 'Scan Leaf';
  static const String leafAnalysis = 'Leaf Analysis';
  static const String leafColorChart = 'Leaf Color Chart';
  static const String captureLeaf = 'Capture Leaf';

  // Recommendations
  static const String fertilizers = 'Fertilizers';
  static const String pesticides = 'Pesticides';
  static const String cropRecommendations = 'Crop Recommendations';
  static const String applicationSchedule = 'Application Schedule';

  // Common
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String update = 'Update';
  static const String submit = 'Submit';
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String success = 'Success';
  static const String retry = 'Retry';
  static const String noData = 'No data available';
  static const String comingSoon = 'Coming Soon';

  // Validation Messages
  static const String fieldRequired = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email';
  static const String passwordTooShort =
      'Password must be at least 6 characters';
  static const String passwordsDoNotMatch = 'Passwords do not match';
}

class AppDimensions {
  // Padding and Margins
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;

  // Icon Sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;

  // Button Heights
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;

  // Card Dimensions
  static const double cardElevation = 2.0;
  static const double cardRadius = 16.0;
}

class AppConstants {
  // API Configuration - Python FastAPI Backend
  static const String baseUrl = 'http://localhost:8000/api';
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String farmDataKey = 'farm_data';
  static const String settingsKey = 'app_settings';

  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int imageQuality = 85;
  static const double maxImageWidth = 1920;
  static const double maxImageHeight = 1080;

  // Location Configuration
  static const double locationAccuracy = 100.0; // meters
  static const int locationTimeout = 30; // seconds

  // Soil Health Thresholds
  static const double soilPhMin = 6.0;
  static const double soilPhMax = 7.5;
  static const double nitrogenMin = 280.0;
  static const double phosphorusMin = 11.0;
  static const double potassiumMin = 120.0;
}
