name: farm_management_app
description: A comprehensive farm management application with soil health monitoring, leaf scanning, and crop recommendations.

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and Navigation
  cupertino_icons: ^1.0.6
  go_router: ^12.1.3
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # HTTP and API
  http: ^1.1.0
  dio: ^5.3.3
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.1

  # Authentication (temporarily disabled for testing)
  # firebase_core: ^2.24.2
  # firebase_auth: ^4.15.3
  # google_sign_in: ^6.1.6

  # Camera and Image Processing
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  image: ^4.1.3
  flutter_image_compress: ^2.1.0

  # Location Services
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.0
  location: ^5.0.3

  # Permissions
  permission_handler: ^11.1.0

  # File Handling
  file_picker: ^6.1.1
  path: ^1.8.3

  # Utilities
  intl: ^0.18.1
  uuid: ^4.2.1
  url_launcher: ^6.2.1
  connectivity_plus: ^5.0.2

  # ML and OCR
  google_ml_kit: ^0.16.3
  tflite_flutter: ^0.10.4

  # Charts and Visualization
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7

  # Notifications (temporarily disabled for testing)
  flutter_local_notifications: ^16.3.0
  # firebase_messaging: ^14.7.10

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^3.0.1

  # Testing
  mockito: ^5.4.2
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/data/
    - assets/ml_models/

  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Roboto-Light.ttf
  #         weight: 300
