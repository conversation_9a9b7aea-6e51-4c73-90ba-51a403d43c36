# SmartCrop Backend

Node.js/Express backend for the farm management app. Handles authentication, farm data, soil health card uploads, leaf color chart analysis, weather, and recommendations.

## Structure

- `config/`: Environment and third-party configs
- `controllers/`: Business logic
- `models/`: Database models
- `routes/`: API endpoints
- `services/`: External integrations
- `utils/`: Helpers
- `tests/`: API tests
- `app.js`: Express app setup
- `server.js`: Entry point

## Getting Started

1. Run `npm install` in this directory.
2. Start the server with `npm start`.

---

See each folder for implementation details and TODOs.
